import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import {
  updateTransaction,
  getTransactionByStripeSessionId,
  getEscrowTransactionByOrderId,
} from "@/services/transactionService";
import { processPaymentSuccess } from "@/services/postPaymentService";
import { addToOrderStateAdmin } from "@/services/ordersAdminServices";
import { OrdersHandlerManager } from "@/lib/api-gateway-handlers/order-handlers";
import { getProfileNameByUserId } from "@/services/usersServices";
import {
  getOrderInfoDesc,
  getOrderStatusDesc,
  OrderActivityType,
  OrderInfo,
  OrderInfoName,
  OrderStatusType,
} from "@/lib/constant";
import { ActivityLog, formatDate } from "@/services/ordersServices";
import { SendMailHandler } from "@/lib/api-gateway-handlers/handlers";
import { NotificationHandlerManager } from "@/lib/api-gateway-handlers/notification-handlers";
import { NotificationEvents } from "@/services/notificationService";
import { getFirestore } from "firebase-admin/firestore";
import admin from "firebase-admin";
import { Timestamp } from "firebase/firestore";
import { UsersHandlerManager } from "@/lib/api-gateway-handlers/users-handler";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);
const webhookSecretDefault = process.env.STRIPE_WEBHOOK_SECRET as string;
const webhookSecretUS = process.env.STRIPE_WEBHOOK_SECRET_US;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get("stripe-signature") as string;

    let event: Stripe.Event;
    let matchedSecret: "default" | "us" | null = null;

    // Try default secret first, then US secret if provided
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecretDefault);
      matchedSecret = "default";
    } catch (errDefault) {
      if (webhookSecretUS) {
        try {
          event = stripe.webhooks.constructEvent(body, signature, webhookSecretUS);
          matchedSecret = "us";
        } catch (errUS) {
          console.error("Webhook signature verification failed for both secrets", { errDefault, errUS });
          return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
        }
      } else {
        console.error("Webhook signature verification failed (no US secret configured)", errDefault);
        return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
      }
    }

    console.log("Stripe webhook verified", { matchedSecret, type: event.type });

    console.log("Received Stripe webhook event:", event.type);

    // Handle the event
    switch (event.type) {
      case "payment_intent.amount_capturable_updated":
        console.log({ data: event?.data?.object?.metadata });
        let data = event?.data?.object?.metadata;
        const [buyerProfileName, sellerProfileName] = await Promise.all([
          UsersHandlerManager.getInstance().GetProfileNameByUserId(data?.userId),
          UsersHandlerManager.getInstance().GetProfileNameByUserId(data?.sellerId)
          // getProfileNameByUserId(data?.userId),
          // getProfileNameByUserId(data?.sellerId),
        ]);

        await OrdersHandlerManager?.getInstance().AddtoOrderState({
          id: data?.orderId,
          loggedInUser: buyerProfileName ?? "",
          sellerName: sellerProfileName ?? "",
          userName: buyerProfileName ?? "",
          sendInvoice: data?.isInvoice === "true",
          payment_intent_id: event?.data?.object?.id,
          dueDate: data?.formateDate ? Timestamp.fromDate(new Date(data?.formateDate)) : undefined,
        });
        break;

      case "transfer.created":
        console.log("🚀 ", { t_data: event?.data?.object?.metadata });
        let transfer_data: any = event?.data?.object?.metadata;
        console.log({ transfer_data });
        await handleTransferPayment(transfer_data);

        break;

      case "payment_intent.canceled":
        console.log("🚀🚀🚀🚀🚀🚀 ", { canceled: event?.data?.object?.metadata });
        let cancel_data: any = event?.data?.object?.metadata;
        await handleTransferPayment(cancel_data);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook error:", error);
    return NextResponse.json({ error: "Webhook handler failed" }, { status: 500 });
  }
}

// Removed handleCheckoutSessionCompleted - no longer using checkout sessions

const handleTransferPayment = async ({
  from,
  sellerName,
  title,
  userName,
  description,
  reason,
  orderId,
  uniqueOrderId,
  newDueDate,
  orderInfoStatus,
  loggedInUser,
}: {
  orderId: string;
  from: "creator" | "user";
  sellerName: string;
  title: OrderStatusType;
  userName: string;
  description?: string;
  reason?: string;
  uniqueOrderId: string;
  newDueDate?: Timestamp;
  orderInfoStatus?: OrderInfo;
  loggedInUser?: string;
}) => {
  try {
    if (title === OrderStatusType.AUTO_COMPLETED || title === OrderStatusType.AUTO_DECLINED) {
      console.log("❌ skipping......");

      // handled in cloud function
      return;
    }
    // order status desc
    const orderStatusDesc = getOrderStatusDesc({
      profileType: from as "creator" | "user",
      sellerName,
      status: title,
      userName,
      comment: description,
      reason,
    });
    const db = getFirestore();
    // update order status
    await db.collection("orders").doc(orderId).update({ status: title });

    const _activityLogRef = db.collection("orders").doc(orderId).collection("activityLog");

    // get last log for previousStatus
    const snapshot = await _activityLogRef
      .where("type", "==", OrderActivityType.orderStatusUpdate)
      .orderBy("date", "desc")
      .limit(1)
      .get();

    let previousStatus: OrderStatusType | undefined;
    if (!snapshot.empty) {
      previousStatus = snapshot.docs[0].data()?.title;
    }
    // payloads
    let activityLogStatusPayload: Partial<ActivityLog> = {
      orderId: uniqueOrderId,
      title,
      date: admin.firestore.Timestamp.now(),
      type: OrderActivityType.orderStatusUpdate,
    };

    let activityLogInfoPayload: Partial<ActivityLog> = {
      orderId: uniqueOrderId,
      date: admin.firestore.Timestamp.now(),
      type: OrderActivityType.orderInfo,
    };
    activityLogStatusPayload = {
      ...activityLogStatusPayload,
      title,
      from,
      description: orderStatusDesc,
    } as ActivityLog;

    // check for revision requests
    const revisionSnapshot = await _activityLogRef
      .where("title", "==", OrderStatusType.REVISION_REQUEST)
      .get();

    if (!revisionSnapshot.empty && orderInfoStatus === OrderInfo.ten) {
      orderInfoStatus = undefined;
    }

    // order info handling
    if (orderInfoStatus) {
      let orderInfoPayload: any = {
        loggedInUser,
        sellerName,
        status: orderInfoStatus,
      };
      if (newDueDate) {
        orderInfoPayload.newDateModel = {
          formattedDate: newDueDate,
          comment: description ?? "",
          reason,
          orderId,
        };
      }
      const orderInfoDesc = getOrderInfoDesc(orderInfoPayload);
      activityLogInfoPayload = {
        ...activityLogInfoPayload,
        title: OrderInfoName[orderInfoStatus],
        description: orderInfoDesc,
      } as ActivityLog;
    }

    if (previousStatus !== undefined) {
      activityLogStatusPayload.previousStatus = previousStatus;
    }
    if (newDueDate !== undefined) {
      activityLogStatusPayload.newDueDate = newDueDate;
      activityLogInfoPayload.newDueDate = newDueDate;
    }
    // add logs
    if (orderInfoStatus) {
      await Promise.all([
        _activityLogRef.add(activityLogStatusPayload),
        _activityLogRef.add(activityLogInfoPayload),
      ]);
    } else {
      await _activityLogRef.add(activityLogStatusPayload);
    }
    // accepted/delivered => update due date
    if (title === OrderStatusType.ACCEPTED || title === OrderStatusType.DELIVERED) {
      await OrdersHandlerManager?.getInstance()?.UpdateOrderDueDate(orderId);
    }

    //
    //------------------------------------ send email
    const orderResult = await OrdersHandlerManager?.getInstance().getOrderById(orderId);
    if (!orderResult?.success) {
      return;
    }
    let uniqueId: string = orderResult?.order?.uniqueId ?? "";
    const seller_mail: string = (
      await OrdersHandlerManager?.getInstance().GetUserInfo(orderResult?.order?.profileId ?? "")
    )?.email;
    const buyer_mail: string = (
      await OrdersHandlerManager?.getInstance().GetUserInfo(orderResult?.order?.userProfileId ?? "")
    )?.email;

    const targetMail = activityLogStatusPayload?.from === "user" ? seller_mail : buyer_mail;

    const targetSrcUserId =
      activityLogStatusPayload?.from === "creator"
        ? orderResult?.order?.profileId
        : orderResult?.order?.userProfileId;
    
    const targetDestUserId =
      activityLogStatusPayload?.from === "creator"
        ? orderResult?.order?.userProfileId
        : orderResult?.order?.profileId;
    

    const targetName =  activityLogStatusPayload?.from === "creator" ? userName : sellerName;
        
    if (title === OrderStatusType.ACCEPTED) {
      await SendMailHandler({
        type: "sendMail",
        mail_type: "order_update",
        message: {
          orderId: uniqueId,
          status: title,
          message: `
                ${activityLogStatusPayload?.description ?? ""}
                Order due date: ${
                  orderResult?.order?.dueDate ? formatDate(orderResult?.order?.dueDate) : ""
                }
              `,
          name:userName
        },
        payload: {
          to: targetMail,
        },
      });
    } else {
      await SendMailHandler({
        type: "sendMail",
        mail_type: "order_update",
        message: {
          orderId: uniqueId,
          status: title,
          message: `${activityLogStatusPayload?.description ?? ""}`,
          name:targetName
        },
        payload: {
          to: targetMail,
        },
      });
    }

    // notify user

    NotificationHandlerManager.getInstance().CreateNotification({
      payload: {
        src_id: targetSrcUserId,
        dest_id: targetDestUserId,
        event: NotificationEvents.ORDER_STATUS_CHANGE,
        order_id: uniqueId,
        order_status: title,
      },
      check_duplicate: false,
    });

    console.log("✅ db write for transfer done");
  } catch (error) {}
};

// Handle other HTTP methods
export async function GET() {
  return NextResponse.json({ message: "Stripe webhook endpoint" });
}

export async function PUT() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}
