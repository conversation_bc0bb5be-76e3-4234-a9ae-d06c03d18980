import { initFirebase } from "../../firebaseConfig";
import { getStorage, ref, getDownloadURL } from "firebase/storage";

// Cache to store resolved URLs globally
let urlCache: Record<string, string> = {};

/**
 * Improved URL generator that handles most cases synchronously on first load
 * This function tries multiple approaches to generate URLs immediately without async delays
 */
export const generateFileUrl = (postFile: string | undefined): string | undefined => {
  if (!postFile) return undefined;

  // If already cached, return immediately
  if (urlCache[postFile]) {
    return urlCache[postFile];
  }

  // Handle ImageKit URLs directly
  if (postFile.includes("https://ik.imagekit.io")) {
    urlCache[postFile] = postFile;
    return postFile;
  }

  // Handle full Firebase Storage URLs directly
  if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
    urlCache[postFile] = postFile;
    return postFile;
  }

  // Try to construct URL using BASE_STORAGE_URL for simple file paths
  const baseUrl = process.env.BASE_STORAGE_URL;
  if (baseUrl) {
    // For simple filenames without paths, construct the URL directly
    if (!postFile.includes("/") && !postFile.includes("?")) {
      const constructedUrl = `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
      urlCache[postFile] = constructedUrl;
      return constructedUrl;
    }
    
    // For paths that look like they might work with BASE_STORAGE_URL
    if (!postFile.startsWith("http")) {
      const constructedUrl = `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
      urlCache[postFile] = constructedUrl;
      return constructedUrl;
    }
  }

  // For complex Firebase paths that need async resolution
  // Return undefined to trigger fallback, and resolve in background
  if (!urlCache[`${postFile}_resolving`]) {
    urlCache[`${postFile}_resolving`] = "true";
    
    (async () => {
      try {
        const { app } = await initFirebase();
        const storage = getStorage(app);

        let filePath = postFile.startsWith("https://firebasestorage.googleapis.com/")
          ? decodeURIComponent(postFile.split("/o/")[1].split("?")[0])
          : postFile;

        const fileRef = ref(storage, filePath);
        const url = await getDownloadURL(fileRef);

        urlCache[postFile] = url;
        delete urlCache[`${postFile}_resolving`];
      } catch (error) {
        console.error("Error resolving Firebase URL:", error);
        delete urlCache[`${postFile}_resolving`];
        // Cache a fallback to prevent repeated attempts
        urlCache[postFile] = "";
      }
    })();
  }

  return undefined; // Return undefined to trigger fallback image
};

/**
 * Clear the URL cache - useful for testing or when you need to refresh URLs
 */
export const clearUrlCache = () => {
  urlCache = {};
};

/**
 * Get the current cache state - useful for debugging
 */
export const getUrlCacheState = () => {
  return { ...urlCache };
};
